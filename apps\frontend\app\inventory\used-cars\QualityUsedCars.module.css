/* Used Vehicles Page Styling - CarGurus Style */

.container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 1rem;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

.heroSection {
  text-align: center;
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  margin-bottom: 2rem;
}

.heroTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.heroSubtitle {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  color: var(--text-secondary);
}

.filterInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0.75rem 1.5rem;
  background: var(--bg-primary, white);
  border-radius: 8px;
  border: 1px solid var(--border-primary, #e2e8f0);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: var(--card-shadow);
}

.filterLabel {
  font-size: 0.9rem;
  color: var(--text-secondary, #718096);
  font-weight: 500;
}

.filterValue {
  font-size: 1rem;
  color: white;
  font-weight: 600;
  background: #e53e3e;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
}

.clearFilter {
  background: transparent;
  border: 1px solid var(--border-primary, #e2e8f0);
  color: var(--text-secondary, #718096);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clearFilter:hover {
  background: var(--bg-primary, white);
  color: var(--text-primary, #2d3748);
  border-color: var(--text-secondary, #718096);
}

.contentWrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.statsSection {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.statCard {
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  padding: 1rem 1.5rem;
  border-radius: 6px;
  box-shadow: var(--card-shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  text-align: center;
  min-width: 120px;
}

.statNumber {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  display: block;
}

.statLabel {
  font-size: 0.8rem;
  color: var(--text-secondary, #718096);
  margin-top: 0.25rem;
}

.vehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Medium screens (638px - 1200px) - Show 2 cards side by side */
@media (max-width: 1200px) and (min-width: 638px) {
  .vehicleGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

.vehicleCard {
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease;
  position: relative;
}

/* Dark mode styling for vehicle cards - match Services page style */
[data-theme="dark"] .vehicleCard {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.vehicleCard:hover {
  box-shadow: var(--card-shadow-hover, 0 4px 12px rgba(0, 0, 0, 0.15));
}

.vehicleImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  border-radius: 12px 12px 0 0;
}

.vehicleImage:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Image Wrapper for positioning compare button */
.imageWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* Status Banners */
.statusBanner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  z-index: 10;
  font-size: 2rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  padding: 0.6rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  border: 3px solid white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  min-width: 160px;
  text-align: center;
}

.statusBanner.sold {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.statusBanner.onHold {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

/* Compare Button */
.compareBtn {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid white;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
}

.compareBtn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.compareBtnActive {
  background: var(--accent-secondary, #10b981);
  border-color: var(--accent-secondary, #10b981);
}

.compareBtnActive:hover {
  background: #0d9488;
  border-color: #0d9488;
}

.compareBtnDisabled {
  background: rgba(128, 128, 128, 0.7);
  cursor: not-allowed;
  opacity: 0.6;
}

.compareBtnDisabled:hover {
  transform: none;
  background: rgba(128, 128, 128, 0.7);
}

.vehicleInfo {
  padding: 1rem;
}

.vehicleTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.vehicleDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: #4a5568;
}

.mileage {
  font-weight: 500;
  color: var(--text-secondary, #4a5568);
}

.vehicleSpecs {
  margin: 0.75rem 0;
  padding: 0.75rem 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.specItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.specItem:last-child {
  margin-bottom: 0;
}

.specLabel {
  color: var(--text-tertiary, #718096);
  font-weight: 600;
}

.specValue {
  color: var(--text-secondary, #4a5568);
  font-weight: 600;
}

/* Vehicle Features */
.vehicleFeatures {
  margin: 0.75rem 0;
  padding: 0.75rem;
  background: var(--bg-tertiary, #f8f9fa);
  border-radius: 6px;
  border-left: 3px solid #48bb78;
}

.featuresTitle {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.4rem;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.8rem;
  color: var(--text-secondary, #4a5568);
}

.featureIcon {
  color: #48bb78;
  font-weight: bold;
  font-size: 0.8rem;
}

.featureText {
  font-weight: 500;
}

.vehiclePrice {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
}

.taxText {
  font-size: 0.95rem;
  font-weight: 400;
  color: var(--text-tertiary, #718096);
}

.estimatedPayment {
  font-size: 0.85rem;
  color: var(--text-tertiary, #718096);
  margin-bottom: 1rem;
}

.buttonGroup {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.viewDetailsBtn {
  flex: 1;
  padding: 0.75rem;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.9rem;
}

.contactBtn {
  flex: 1;
  padding: 0.75rem;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.9rem;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 2rem;
  padding: 3rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 1.1rem;
  color: #4a5568;
}

/* Error State */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 3rem;
  margin: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.errorIcon {
  font-size: 4rem;
  color: #e53e3e;
  margin-bottom: 1rem;
}

.errorText {
  font-size: 1.2rem;
  color: #2d3748;
  margin-bottom: 1.5rem;
}

.retryBtn {
  background: #4299e1;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryBtn:hover {
  background: #3182ce;
}

/* Empty State */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 8px;
  padding: 3rem;
  margin: 2rem;
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
}

.emptyIcon {
  font-size: 4rem;
  color: var(--text-tertiary, #a0aec0);
  margin-bottom: 1rem;
}

.emptyText {
  font-size: 1.2rem;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.5rem;
}

.emptySubtext {
  color: var(--text-secondary, #718096);
  font-size: 1rem;
}

/* Responsive Design */
/* Small-Medium screens (637px - 769px) - Single column */
@media (max-width: 637px) and (min-width: 481px) {
  .vehicleGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 0.75rem; /* Reduce side padding to use more space */
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1rem;
  }

  .vehicleGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 0.75rem; /* Reduce side padding to use more space */
  }

  .statsSection {
    gap: 1rem;
  }

  .statCard {
    padding: 1rem 1.5rem;
    min-width: 120px;
  }

  .vehicleDetails {
    grid-template-columns: 1fr;
    gap: 0.6rem;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* Mobile screens - Reduce card width by 10% to maintain card look */
@media (max-width: 480px) {
  .container {
    padding-top: 1rem;
    padding-left: 0;
    padding-right: 0;
  }

  .heroSection {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .contentWrapper {
    padding: 0 5%; /* 5% padding on each side = 10% total width reduction */
    margin: 0;
  }

  .vehicleGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0;
    margin: 0;
  }

  .vehicleCard {
    margin: 0; /* Remove any default margins */
    width: 100%; /* Ensure full width usage within the padded container */
    border-radius: 12px;
  }

  .cardContent {
    padding: 1rem;
  }
}
