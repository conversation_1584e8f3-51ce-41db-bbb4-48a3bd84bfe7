/* Vehicle Card Component Styling */

.vehicleCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid transparent;
  width: 100%; /* Ensure full width usage */
}

/* Dark mode styling for vehicle cards - match Services page style */
[data-theme="dark"] .vehicleCard {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.vehicleCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.imageWrapper {
  position: relative;
  height: 250px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

/* Status Banners */
.statusBanner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  z-index: 10;
  font-size: 2rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  padding: 0.6rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  border: 3px solid white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  min-width: 160px;
  text-align: center;
}

.statusBanner.sold {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.statusBanner.onHold {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.vehicleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.vehicleCard:hover .vehicleImage {
  transform: scale(1.05);
}

.priceTag {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #48bb78;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Compare Button */
.compareBtn {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid white;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
}

.compareBtn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.compareBtnActive {
  background: var(--accent-secondary, #10b981);
  border-color: var(--accent-secondary, #10b981);
}

.compareBtnActive:hover {
  background: #0d9488;
  border-color: #0d9488;
}

.compareBtnDisabled {
  background: rgba(128, 128, 128, 0.7);
  cursor: not-allowed;
  opacity: 0.6;
}

.compareBtnDisabled:hover {
  transform: none;
  background: rgba(128, 128, 128, 0.7);
}

.vehicleInfo {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicleInfo h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.vehicleInfo p {
  margin: 0.25rem 0;
  color: #4a5568;
  font-size: 0.95rem;
}

.cardActions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 1rem;
}

.editBtn,
.deleteBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.editBtn {
  background: #4299e1;
  color: white;
}

.editBtn:hover {
  background: #3182ce;
}

.deleteBtn {
  background: #e53e3e;
  color: white;
}

.deleteBtn:hover {
  background: #c53030;
}

/* Responsive Design */
@media (max-width: 768px) {
  .imageWrapper {
    height: 200px;
  }

  .vehicleInfo {
    padding: 1rem;
  }

  .vehicleInfo h3 {
    font-size: 1.3rem;
  }

  .cardActions {
    flex-direction: column;
  }
}

/* Mobile screens - maximize card width to match red outline */
@media (max-width: 480px) {
  .vehicleCard {
    margin: 0; /* Remove any default margins */
    width: 100%; /* Ensure full width usage */
    max-width: none; /* Remove any max-width constraints */
  }

  .vehicleInfo {
    padding: 1rem 1.25rem; /* Slightly more padding for better content spacing */
  }
}
