/* Admin Listings Page Styles */
.container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
}

.header h1 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 2rem;
  font-weight: 600;
}

.header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Filters */
.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filterGroup label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.filterInput,
.filterSelect {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-background);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.filterInput:focus,
.filterSelect:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

/* Error State */
.error {
  background: var(--error-background);
  border: 1px solid var(--error-border);
  color: var(--error-text);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Vehicle Grid */
.vehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.vehicleCard {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.vehicleCard:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-background);
}

.cardHeader h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.active {
  background: var(--success-background);
  color: var(--success-text);
}

.status.draft {
  background: var(--warning-background);
  color: var(--warning-text);
}

.status.sold {
  background: var(--info-background);
  color: var(--info-text);
}

.status.on_hold {
  background: var(--warning-background);
  color: var(--warning-text);
}

.status.archived {
  background: var(--muted-background);
  color: var(--muted-text);
}

.cardContent {
  padding: 20px;
}

/* Image Preview */
.imagePreview {
  width: 100%;
  height: 120px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--muted-background);
  display: flex;
  align-items: center;
  justify-content: center;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.noImage {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Details */
.details {
  margin-bottom: 16px;
}

.detailRow {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 60px;
  font-size: 0.85rem;
}

.uuidContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.uuid,
.mongoId {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  background: var(--code-background);
  padding: 4px 6px;
  border-radius: 4px;
  color: var(--code-text);
  word-break: break-all;
}

.copyButton {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.2s ease;
}

.copyButton:hover {
  background: var(--primary-color-dark);
}

/* Media Info */
.mediaInfo {
  flex: 1;
}

.mediaDetails {
  font-size: 0.85rem;
}

.mediaDetails summary {
  cursor: pointer;
  color: var(--primary-color);
  font-weight: 500;
}

.mediaDetails summary:hover {
  text-decoration: underline;
}

.mediaList {
  margin-top: 8px;
  padding-left: 16px;
}

.mediaItem {
  margin-bottom: 4px;
  font-size: 0.8rem;
}

.mediaType {
  font-weight: 500;
  color: var(--text-secondary);
}

.mediaKey {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--code-background);
  padding: 2px 4px;
  border-radius: 3px;
  color: var(--code-text);
  font-size: 0.75rem;
}

.noMedia {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

/* Actions */
.actions {
  display: flex;
  gap: 8px;
}

.editButton,
.viewButton {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton {
  background: var(--primary-color);
  color: white;
}

.editButton:hover {
  background: var(--primary-color-dark);
}

.viewButton {
  background: var(--secondary-background);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.viewButton:hover {
  background: var(--hover-background);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}

.pageButton {
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.pageButton:hover:not(:disabled) {
  background: var(--primary-color-dark);
}

.pageButton:disabled {
  background: var(--muted-background);
  color: var(--muted-text);
  cursor: not-allowed;
}

.pageInfo {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* No Results */
.noResults {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .vehicleGrid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    grid-template-columns: 1fr;
  }
  
  .cardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detailRow {
    flex-direction: column;
    gap: 4px;
  }
  
  .uuidContainer {
    width: 100%;
  }
}
