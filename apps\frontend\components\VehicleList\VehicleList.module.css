


 .vehicleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

.vehicleCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
  position: relative;
}

.vehicleCard:hover {
  transform: translateY(-4px);
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.vehicleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.priceTag {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #d90429;
  color: white;
  padding: 0.4rem 0.8rem;
  font-weight: bold;
  border-radius: 6px;
  font-size: 1rem;
}

.vehicleCard h3 {
  font-size: 1.2rem;
  margin: 0.8rem;
  margin-bottom: 0.2rem;
}

.vehicleCard p {
  margin: 0.2rem 0.8rem;
  font-size: 0.95rem;
  color: #333;
}

.actions {
  display: flex;
  justify-content: space-between;
  padding: 0.8rem;
  margin-top: auto;
}

.actions button {
  background-color: #0070f3;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.actions button:hover {
  background-color: #005bb5;
}

.deleteButton {
  background-color: #e63946;
}

.deleteButton:hover {
  background-color: #b02a37;
}

/* Responsive Design */
/* Large screens - auto-fill with minimum 450px */
@media (min-width: 1200px) {
  .vehicleGrid {
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  }
}

/* Medium screens (638px - 1200px) - Show 2 cards side by side */
@media (max-width: 1200px) and (min-width: 638px) {
  .vehicleGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Small screens (below 638px) - Single column */
@media (max-width: 637px) {
  .vehicleGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem;
  }

  .vehicleCard {
    margin: 0 auto;
    max-width: 400px;
  }
}

/* Mobile screens - MAXIMIZE card width by removing ALL padding/margins */
@media (max-width: 480px) {
  .vehicleGrid {
    padding: 0; /* Remove ALL padding to maximize card width */
    margin: 0; /* Remove ALL margins */
  }

  .vehicleCard {
    margin: 0; /* Remove margins */
    max-width: none; /* Remove max-width constraint */
    width: 100%; /* Use full available width */
  }
}
