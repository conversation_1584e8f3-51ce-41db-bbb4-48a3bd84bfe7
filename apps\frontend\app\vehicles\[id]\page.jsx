'use client';
import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import styles from './VehicleDetails.module.css';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../lib/auth';

export default function VehicleDetailsPage() {
  const { id } = useParams();
  const router = useRouter();
  const { t } = useLanguage();
  const { user, isDealer, isAdmin, hasRole } = useAuth();
  const [vehicle, setVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [imageContainerTouched, setImageContainerTouched] = useState(false);

  // Check if user has admin/dealer/salesperson access
  const hasAdminAccess = user && (isAdmin || isDealer || hasRole('salesman'));

  // Helper functions for image gallery (now includes video)
  const getVehicleImages = () => {
    if (!vehicle) return [];
    let media = [];

    // Add images first
    if (vehicle.images && vehicle.images.length > 0) {
      media = [...vehicle.images];
    } else if (vehicle.imageUrl) {
      media = [vehicle.imageUrl];
    }

    // Add video at the end if it exists
    if (vehicle.videoUrl) {
      media.push({ type: 'video', url: vehicle.videoUrl });
    }

    return media;
  };

  // Check if current item is a video
  const isCurrentItemVideo = () => {
    const media = getVehicleImages();
    const currentItem = media[currentImageIndex];
    return currentItem && typeof currentItem === 'object' && currentItem.type === 'video';
  };

  const nextImage = () => {
    const media = getVehicleImages();
    setCurrentImageIndex((prev) => (prev + 1) % media.length);
  };

  const prevImage = () => {
    const media = getVehicleImages();
    setCurrentImageIndex((prev) => (prev - 1 + media.length) % media.length);
  };

  const selectImage = (index) => {
    setCurrentImageIndex(index);
  };

  // Handle touch/click for showing navigation arrows
  const handleImageContainerClick = () => {
    setImageContainerTouched(true);
    // Hide arrows after 3 seconds on mobile
    setTimeout(() => {
      setImageContainerTouched(false);
    }, 3000);
  };

  useEffect(() => {
    const fetchVehicle = async () => {
      try {
        const res = await fetch(`/api/vehicles/${id}`);
        const data = await res.json();

        if (data.success) {
          setVehicle(data.data);
        } else {
          setError('Vehicle not found');
        }
      } catch (err) {
        setError('Failed to load vehicle details');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchVehicle();
    }
  }, [id]);

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>{t('common.loading_vehicle_details')}</p>
        </div>
      </div>
    );
  }

  if (error || !vehicle) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <h2>{t('vehicle.not_found')}</h2>
          <p>{error || t('vehicle.not_found_message')}</p>
          <button
            onClick={() => router.push('/inventory/used-cars')}
            className={styles.backBtn}
          >
            {t('vehicle.back_to_inventory')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <button
          onClick={() => router.push('/inventory/used-cars')}
          className={styles.backBtn}
        >
          {t('vehicle.back_to_inventory')}
        </button>

        {/* Header Badges */}
        {vehicle.status === 'sold' && (
          <div className={styles.headerBadge}>
            <span className={styles.headerBadgeText}>{t('vehicle.sold')}</span>
          </div>
        )}
        {vehicle.status === 'on_hold' && (
          <div className={styles.headerBadge}>
            <span className={`${styles.headerBadgeText} ${styles.onHold}`}>ON HOLD</span>
          </div>
        )}
      </div>

      {/* Title Section - Above image on desktop (768px+) */}
      <div className={styles.desktopTitleSection}>
        <h1 className={styles.vehicleTitle}>
          {vehicle.year} {vehicle.make} {vehicle.model}
        </h1>
        <div className={styles.priceSection}>
          <span className={styles.price}>
            ${vehicle.price?.toLocaleString() || 'N/A'}
          </span>
          <span className={styles.taxText}>{t('vehicle.plus_tax')}</span>
        </div>
        <div className={styles.estimatedPayment}>
          {t('vehicle.estimated_payment')} ${Math.round((vehicle.price || 0) / 60)}{t('vehicle.per_month')}
        </div>
      </div>

      <div className={styles.vehicleDetails}>
        {/* Image Gallery Section */}
        <div className={styles.imageSection}>
          <div
            className={`${styles.mainImageContainer} ${imageContainerTouched ? styles.touched : ''}`}
            onClick={handleImageContainerClick}
          >
            {isCurrentItemVideo() ? (
              <video
                controls
                className={styles.mainImage}
                poster={getVehicleImages()[0] && typeof getVehicleImages()[0] === 'string' ? getVehicleImages()[0] : vehicle.imageUrl}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <source src={getVehicleImages()[currentImageIndex].url} type="video/mp4" />
                <source src={getVehicleImages()[currentImageIndex].url} type="video/webm" />
                <source src={getVehicleImages()[currentImageIndex].url} type="video/mov" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <img
                src={typeof getVehicleImages()[currentImageIndex] === 'string'
                  ? getVehicleImages()[currentImageIndex]
                  : getVehicleImages()[currentImageIndex]?.url || vehicle.imageUrl}
                alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                className={styles.mainImage}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowImageModal(true);
                }}
              />
            )}
            {getVehicleImages().length > 1 && (
              <>
                <button
                  className={styles.prevBtn}
                  onClick={(e) => {
                    e.stopPropagation();
                    prevImage();
                  }}
                >
                  &#8249;
                </button>
                <button
                  className={styles.nextBtn}
                  onClick={(e) => {
                    e.stopPropagation();
                    nextImage();
                  }}
                >
                  &#8250;
                </button>
              </>
            )}
          </div>

          {/* Thumbnail Gallery */}
          {getVehicleImages().length > 1 && (
            <div className={styles.thumbnailGallery}>
              {getVehicleImages().map((item, index) => {
                const isVideo = typeof item === 'object' && item.type === 'video';
                const thumbnailSrc = isVideo
                  ? (getVehicleImages()[0] && typeof getVehicleImages()[0] === 'string'
                      ? getVehicleImages()[0]
                      : vehicle.imageUrl)
                  : (typeof item === 'string' ? item : item.url);

                return (
                  <div
                    key={index}
                    className={`${styles.thumbnailWrapper} ${index === currentImageIndex ? styles.activeThumbnail : ''}`}
                    onClick={() => selectImage(index)}
                  >
                    <img
                      src={thumbnailSrc}
                      alt={`${vehicle.year} ${vehicle.make} ${vehicle.model} - ${isVideo ? 'Video' : `Image ${index + 1}`}`}
                      className={styles.thumbnail}
                    />
                    {isVideo && (
                      <div className={styles.videoIndicator}>
                        <span className={styles.playIcon}>▶</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* Video is now integrated into the image gallery above */}

          {/* Description Section - Positioned under images */}
          {vehicle.description && (
            <div className={styles.descriptionSection}>
              <h3 className={styles.descriptionTitle}>{t('vehicle.description')}</h3>
              <p className={styles.descriptionText}>{vehicle.description}</p>
            </div>
          )}


        </div>

        {/* Vehicle Information */}
        <div className={styles.infoSection}>
          <div className={styles.titleSection}>
            <h1 className={styles.vehicleTitle}>
              {vehicle.year} {vehicle.make} {vehicle.model}
            </h1>
            <div className={styles.priceSection}>
              <span className={styles.price}>
                ${vehicle.price?.toLocaleString() || 'N/A'}
              </span>
              <span className={styles.taxText}>{t('vehicle.plus_tax')}</span>
            </div>
            <div className={styles.estimatedPayment}>
              {t('vehicle.estimated_payment')} ${Math.round((vehicle.price || 0) / 60)}{t('vehicle.per_month')}
            </div>
          </div>

          {/* Vehicle Details Section */}
          <div className={styles.detailsSection}>
            <h2>{t('vehicle.details')}</h2>
            <div className={styles.detailsList}>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.make')}</span>
                <span className={styles.detailValue}>{vehicle.make || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.model')}</span>
                <span className={styles.detailValue}>{vehicle.model || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.year')}</span>
                <span className={styles.detailValue}>{vehicle.year || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.odometer')}</span>
                <span className={styles.detailValue}>{vehicle.mileage ? `${vehicle.mileage.toLocaleString()} ${t('vehicle.km')}` : 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.engine')}</span>
                <span className={styles.detailValue}>{vehicle.engine || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.transmission')}</span>
                <span className={styles.detailValue}>{vehicle.transmission || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.drivetrain')}</span>
                <span className={styles.detailValue}>{vehicle.drivetrain || vehicle.driveline || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.fuel_type')}</span>
                <span className={styles.detailValue}>{vehicle.fuelType || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.body_class')}</span>
                <span className={styles.detailValue}>{vehicle.bodyType || vehicle.bodyClass || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.doors')}</span>
                <span className={styles.detailValue}>{vehicle.doors || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.cylinders')}</span>
                <span className={styles.detailValue}>{vehicle.cylinders || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.exterior_color')}</span>
                <span className={styles.detailValue}>{vehicle.color || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.interior_color')}</span>
                <span className={styles.detailValue}>{vehicle.interiorColor || 'N/A'}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>{t('vehicle.vin')}</span>
                <span className={styles.detailValue}>{vehicle.vin || 'N/A'}</span>
              </div>

              {/* Admin-only information */}
              {hasAdminAccess && (
                <>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>UUID</span>
                    <span className={styles.detailValue}>{vehicle.uuid || 'N/A'}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>ID</span>
                    <span className={styles.detailValue}>{vehicle._id || vehicle.id || 'N/A'}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Media Count</span>
                    <span className={styles.detailValue}>
                      {(() => {
                        let mediaCount = 0;
                        if (vehicle.images && vehicle.images.length > 0) {
                          mediaCount += vehicle.images.length;
                        } else if (vehicle.imageUrl) {
                          mediaCount += 1;
                        }
                        if (vehicle.videoUrl) {
                          mediaCount += 1;
                        }
                        return `${mediaCount} file(s)`;
                      })()}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Features Section */}
          {vehicle.features && Object.entries(vehicle.features).some(([category, categoryFeatures]) => {
            if (Array.isArray(categoryFeatures)) {
              return categoryFeatures.length > 0;
            } else if (typeof categoryFeatures === 'object') {
              return Object.keys(categoryFeatures).length > 0;
            }
            return false;
          }) && (
            <div className={styles.featuresSection}>
              <h3>{t('vehicle.features_options')}</h3>

              {/* Show key features initially */}
              <div className={styles.keyFeatures}>
                {Object.entries(vehicle.features).slice(0, 1).map(([category, features]) => {
                  if (!features) return null;

                  // Handle both array and object formats
                  let featuresArray = [];
                  if (Array.isArray(features)) {
                    featuresArray = features;
                  } else if (typeof features === 'object') {
                    featuresArray = Object.entries(features).map(([key, value]) => ({
                      value: key,
                      status: value ? 'included' : 'not_available'
                    }));
                  }

                  if (featuresArray.length === 0) return null;

                  return featuresArray.slice(0, 6).map((feature, index) => {
                    const featureValue = typeof feature === 'string' ? feature : feature.value;
                    const featureStatus = typeof feature === 'string' ? 'included' : feature.status;

                    return (
                      <div key={`${category}-${index}`} className={styles.keyFeatureItem}>
                        <span className={styles.keyFeatureName}>{featureValue}</span>
                      </div>
                    );
                  });
                })}
              </div>

              {/* View All Options Button */}
              <button
                className={styles.viewAllBtn}
                onClick={() => {
                  const fullFeaturesSection = document.getElementById('fullFeaturesSection');
                  if (fullFeaturesSection) {
                    fullFeaturesSection.style.display = fullFeaturesSection.style.display === 'none' ? 'block' : 'none';
                  }
                }}
              >
                ⚙ {t('vehicle.view_all_options')}
              </button>

              {/* Full Features Section (Initially Hidden) */}
              <div id="fullFeaturesSection" className={styles.fullFeaturesSection} style={{display: 'none'}}>
                <div className={styles.featuresGrid}>
                  {Object.entries(vehicle.features).map(([category, features]) => {
                    if (!features) return null;

                    // Handle both array and object formats
                    let featuresArray = [];
                    if (Array.isArray(features)) {
                      featuresArray = features;
                    } else if (typeof features === 'object') {
                      featuresArray = Object.entries(features).map(([key, value]) => ({
                        value: key,
                        status: value ? 'included' : 'not_available'
                      }));
                    }

                    if (featuresArray.length === 0) return null;

                    return (
                      <div key={category} className={styles.featureCategory}>
                        <h4 className={styles.categoryTitle}>{category}</h4>
                        <div className={styles.categoryFeatures}>
                          {featuresArray.map((feature, index) => {
                            const featureValue = typeof feature === 'string' ? feature : feature.value;
                            const featureStatus = typeof feature === 'string' ? 'included' : feature.status;

                            return (
                              <div key={index} className={`${styles.featureItem} ${styles[featureStatus]}`}>
                                <span className={styles.featureIcon}>
                                  {featureStatus === 'included' ? '✓' : '✗'}
                                </span>
                                <span className={styles.featureText}>
                                  {featureValue}
                                </span>
                                <span className={styles.featureStatus}>
                                  {featureStatus === 'included' ? t('vehicle.included') : t('vehicle.not_available')}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className={styles.actionButtons}>
            <button className={`glossy-button contact-btn ${styles.contactBtn}`}>
              {t('vehicle.contact_about')}
            </button>
            <button className={`glossy-button view-btn ${styles.financeBtn}`}>
              {t('vehicle.get_financing')}
            </button>
          </div>

          {/* Disclaimer */}
          <div className={styles.disclaimer}>
            <p><strong>{t('vehicle.note')}</strong> {t('vehicle.disclaimer')}</p>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div className={styles.imageModal} onClick={() => setShowImageModal(false)}>
          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
            <button className={styles.closeModal} onClick={() => setShowImageModal(false)}>
              ×
            </button>
            <img
              src={getVehicleImages()[currentImageIndex]}
              alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
              className={styles.modalImage}
            />
            {getVehicleImages().length > 1 && (
              <>
                <button className={styles.modalPrevBtn} onClick={prevImage}>
                  &#8249;
                </button>
                <button className={styles.modalNextBtn} onClick={nextImage}>
                  &#8250;
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
