'use client';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './newTransaction.module.css';

export default function NewTransactionPage() {
  const router = useRouter();
  const fileInputRef = useRef(null);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [vehicleSearch, setVehicleSearch] = useState('');
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [vehicleSearchLoading, setVehicleSearchLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);

  const [formData, setFormData] = useState({
    type: '',
    description: '',
    amount: '',
    currency: 'CAD',
    date: new Date().toISOString().split('T')[0],
    vehicleId: '',
    notes: ''
  });

  const transactionTypes = [
    'Vehicle Purchase',
    'Repair Cost',
    'General Cost',
    'Sale',
    'Insurance',
    'Registration',
    'Inspection',
    'Marketing',
    'Office Supplies',
    'Utilities',
    'Other Income',
    'Other Expense'
  ];

  const currencies = [
    { code: 'CAD', name: 'Canadian Dollar' },
    { code: 'USD', name: 'US Dollar' },
    { code: 'EUR', name: 'Euro' }
  ];

  // Get current user from localStorage
  const getCurrentUser = () => {
    try {
      const user = localStorage.getItem('user');
      return user ? JSON.parse(user) : null;
    } catch {
      return null;
    }
  };

  // Search vehicles when search term changes
  useEffect(() => {
    const searchVehicles = async () => {
      if (vehicleSearch.length < 2) {
        setVehicleOptions([]);
        return;
      }

      setVehicleSearchLoading(true);
      try {
        const response = await fetch(`/api/ledger/vehicles?search=${encodeURIComponent(vehicleSearch)}&limit=10`);
        const data = await response.json();
        
        if (data.success) {
          setVehicleOptions(data.data);
        }
      } catch (error) {
        console.error('Error searching vehicles:', error);
      } finally {
        setVehicleSearchLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchVehicles, 300);
    return () => clearTimeout(debounceTimer);
  }, [vehicleSearch]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleVehicleSelect = (vehicle) => {
    setFormData(prev => ({
      ...prev,
      vehicleId: vehicle._id
    }));
    setVehicleSearch(vehicle.label);
    setVehicleOptions([]);
  };

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    const validFiles = [];
    const errors = [];

    files.forEach(file => {
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: Unsupported file type. Please use PDF, JPG, or PNG.`);
        return;
      }

      if (file.size > maxSize) {
        errors.push(`${file.name}: File too large. Maximum size is 10MB.`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      setError(errors.join('\n'));
    } else {
      setError(null);
    }

    setSelectedFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    const currentUser = getCurrentUser();
    if (!currentUser) {
      setError('Please log in to add transactions');
      return;
    }

    // Validate required fields
    if (!formData.type || !formData.description || !formData.amount) {
      setError('Please fill in all required fields');
      return;
    }

    if (parseFloat(formData.amount) <= 0) {
      setError('Amount must be greater than 0');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const submitFormData = new FormData();
      
      // Add form fields
      submitFormData.append('type', formData.type);
      submitFormData.append('description', formData.description);
      submitFormData.append('amount', formData.amount);
      submitFormData.append('currency', formData.currency);
      submitFormData.append('date', formData.date);
      submitFormData.append('addedBy', currentUser.email);
      
      if (formData.vehicleId) {
        submitFormData.append('vehicleId', formData.vehicleId);
      }
      
      if (formData.notes) {
        submitFormData.append('notes', formData.notes);
      }

      // Add receipt files
      selectedFiles.forEach(file => {
        submitFormData.append('receipts', file);
      });

      const response = await fetch('/api/ledger', {
        method: 'POST',
        body: submitFormData
      });

      if (!response.ok) {
        const errorText = await response.text();
        setError(`Server error (${response.status}): ${errorText}`);
        return;
      }

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        setTimeout(() => {
          router.push('/admin/ledger');
        }, 2000);
      } else {
        setError(data.error || 'Failed to create transaction');
      }
    } catch (error) {
      console.error('Error creating transaction:', error);
      setError(`Network error: ${error.message}. Please check your connection and try again.`);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (success) {
    return (
      <div className={styles.container}>
        <div className={styles.successMessage}>
          <div className={styles.successIcon}>✅</div>
          <h2>Transaction Created Successfully!</h2>
          <p>Redirecting to ledger...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1>➕ Add New Transaction</h1>
          <p>Record a new financial transaction with receipts</p>
        </div>
        
        <Link href="/admin/ledger" className={styles.backButton}>
          ← Back to Ledger
        </Link>
      </div>

      {error && (
        <div className={styles.error}>
          <p>❌ {error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGrid}>
          {/* Transaction Type */}
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Transaction Type <span className={styles.required}>*</span>
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              className={styles.select}
              required
            >
              <option value="">Select transaction type</option>
              {transactionTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Amount */}
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Amount <span className={styles.required}>*</span>
            </label>
            <div className={styles.amountGroup}>
              <input
                type="number"
                step="0.01"
                min="0.01"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                className={styles.input}
                placeholder="0.00"
                required
              />
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                className={styles.currencySelect}
              >
                {currencies.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.code}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Date */}
          <div className={styles.formGroup}>
            <label className={styles.label}>Date</label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className={styles.input}
            />
          </div>
        </div>

        {/* Description */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Description <span className={styles.required}>*</span>
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className={styles.textarea}
            placeholder="Describe the transaction..."
            rows={3}
            maxLength={500}
            required
          />
          <small className={styles.charCount}>
            {formData.description.length}/500 characters
          </small>
        </div>

        {/* Vehicle Search */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Vehicle (Optional)</label>
          <div className={styles.vehicleSearch}>
            <input
              type="text"
              value={vehicleSearch}
              onChange={(e) => setVehicleSearch(e.target.value)}
              className={styles.input}
              placeholder="Search by make, model, year, or VIN..."
            />
            {vehicleSearchLoading && (
              <div className={styles.searchLoading}>Searching...</div>
            )}
            {vehicleOptions.length > 0 && (
              <div className={styles.vehicleOptions}>
                {vehicleOptions.map(vehicle => (
                  <div
                    key={vehicle._id}
                    className={styles.vehicleOption}
                    onClick={() => handleVehicleSelect(vehicle)}
                  >
                    <strong>{vehicle.label}</strong>
                    <small>{vehicle.status}</small>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Receipt Upload */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Receipt Files</label>
          <div className={styles.fileUpload}>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={handleFileSelect}
              className={styles.fileInput}
            />
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className={styles.fileButton}
            >
              📎 Choose Files
            </button>
            <small className={styles.fileHint}>
              Supported: PDF, JPG, PNG (Max 10MB each)
            </small>
          </div>

          {selectedFiles.length > 0 && (
            <div className={styles.fileList}>
              <h4>Selected Files:</h4>
              {selectedFiles.map((file, index) => (
                <div key={index} className={styles.fileItem}>
                  <span className={styles.fileName}>{file.name}</span>
                  <span className={styles.fileSize}>{formatFileSize(file.size)}</span>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className={styles.removeFile}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Notes */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Notes (Optional)</label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            className={styles.textarea}
            placeholder="Additional notes or comments..."
            rows={3}
            maxLength={1000}
          />
          <small className={styles.charCount}>
            {formData.notes.length}/1000 characters
          </small>
        </div>

        {/* Submit Button */}
        <div className={styles.submitSection}>
          <button
            type="submit"
            disabled={loading}
            className={styles.submitButton}
          >
            {loading ? '💾 Creating Transaction...' : '💾 Create Transaction'}
          </button>
        </div>
      </form>
    </div>
  );
}
